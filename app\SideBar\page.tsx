"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  MessageCircle,
  BookOpen,
  Sparkles,
  ChevronLeft,
  ChevronRight,
  Menu,
  X,
  Settings,
  Crown,
  ArrowRight,
  LogOut
} from "lucide-react"
import { useAuth } from "@/lib/auth-context"
import { logOut } from "@/lib/auth"
import { cn } from "@/lib/utils"
import { USER_TIERS } from "@/lib/firestore"

interface SidebarProps {
  className?: string
}

export function Sidebar({ className }: SidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [isMobileOpen, setIsMobileOpen] = useState(false)
  const pathname = usePathname()
  const router = useRouter()
  const { user, userDocument } = useAuth()

  const navigationItems = [
    {
      title: "Chat",
      href: "/Chat",
      icon: MessageCircle,
      description: "AI Chat Hub"
    },
    {
      title: "Models",
      href: "/Models",
      icon: Sparkles,
      description: "AI Models & Tools"
    },
    {
      title: "Contact Us",
      href: "/ContactUs",
      icon: MessageCircle,
      description: "Get in touch"
    },
    {
      title: "Privacy",
      href: "/Privacy",
      icon: BookOpen,
      description: "Privacy Policy"
    },
    {
      title: "Terms",
      href: "/Terms",
      icon: BookOpen,
      description: "Terms of Service"
    }
  ]

  const toggleCollapse = () => setIsCollapsed(!isCollapsed)
  const toggleMobile = () => setIsMobileOpen(!isMobileOpen)

  // Handle sign out
  const handleSignOut = async () => {
    try {
      await logOut()
      // Redirect to sign-in page after successful logout
      router.push('/SignInPage')
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  // Upgrade notification component
  const UpgradeNotification = () => {
    // Only show if user is authenticated, not loading, and has starter tier
    if (!user || !userDocument || userDocument.tier !== USER_TIERS.STARTER) {
      return null
    }

    const handleUpgradeClick = () => {
      // Placeholder for upgrade link - will be updated later
      console.log('Upgrade clicked')
      // TODO: Navigate to upgrade page
    }

    return (
      <div className="mb-3">
        <Button
          onClick={handleUpgradeClick}
          variant="ghost"
          className={cn(
            "w-full justify-start h-auto p-3 transition-all duration-200 relative overflow-hidden",
            "bg-gradient-to-r from-amber-50 to-orange-50 hover:from-amber-100 hover:to-orange-100",
            "border border-amber-200 hover:border-amber-300 rounded-lg",
            "text-amber-800 hover:text-amber-900",
            isCollapsed ? "px-2" : "px-3"
          )}
        >
          <div className="flex items-center w-full min-w-0">
            <Crown className={cn("flex-shrink-0 text-amber-600", isCollapsed ? "w-5 h-5" : "w-5 h-5 mr-3")} />
            {!isCollapsed && (
              <div className="flex-1 text-left min-w-0 mr-2">
                <div className="font-semibold text-sm truncate">Upgrade to Pro</div>
                <div className="text-xs opacity-75 leading-tight line-clamp-2">
                  Unlock premium features
                </div>
              </div>
            )}
            {!isCollapsed && (
              <ArrowRight className="w-4 h-4 flex-shrink-0 opacity-60" />
            )}
          </div>
        </Button>
      </div>
    )
  }

  const SidebarContent = () => (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center flex-shrink-0">
            <Sparkles className="w-6 h-6 text-white" />
          </div>
          {!isCollapsed && (
            <div className="min-w-0">
              <h1 className="text-xl font-bold text-gray-900 truncate">Promptly</h1>
              <p className="text-sm text-gray-500 truncate">AI Platform</p>
            </div>
          )}
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2">
        {navigationItems.map((item) => {
          const isActive = pathname === item.href
          const Icon = item.icon

          return (
            <div key={item.href} className="relative">
              <Link href={item.href}>
                <Button
                  variant={isActive ? "default" : "ghost"}
                  className={cn(
                    "w-full justify-start h-12 transition-all duration-200",
                    isActive
                      ? "bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white shadow-lg"
                      : "hover:bg-gray-100 text-gray-700 hover:text-gray-900",
                    isCollapsed ? "px-3" : "px-4"
                  )}
                >
                  <Icon className={cn("flex-shrink-0", isCollapsed ? "w-5 h-5" : "w-5 h-5 mr-3")} />
                  {!isCollapsed && (
                    <div className="flex-1 text-left min-w-0">
                      <div className="font-medium truncate">{item.title}</div>
                      <div className="text-xs opacity-75 truncate">{item.description}</div>
                    </div>
                  )}
                </Button>
              </Link>
            </div>
          )
        })}
      </nav>

      {/* User Profile */}
      <div className="p-4 border-t border-gray-200 space-y-3">
        {/* Upgrade Notification */}
        <UpgradeNotification />

        {/* Settings Button */}
        <Link href="/dashboard/settings">
          <Button
            variant="ghost"
            className={cn(
              "w-full justify-start h-10 transition-all duration-200",
              "hover:bg-gray-100 text-gray-700 hover:text-gray-900",
              isCollapsed ? "px-3" : "px-4"
            )}
          >
            <Settings className={cn("flex-shrink-0", isCollapsed ? "w-5 h-5" : "w-5 h-5 mr-3")} />
            {!isCollapsed && (
              <span className="font-medium">Settings</span>
            )}
          </Button>
        </Link>

        {/* Sign Out Button */}
        <Button
          variant="ghost"
          onClick={handleSignOut}
          className={cn(
            "w-full justify-start h-10 transition-all duration-200",
            "hover:bg-gray-100 text-gray-700 hover:text-gray-900",
            isCollapsed ? "px-3" : "px-4"
          )}
          aria-label="Sign out"
        >
          <LogOut className={cn("flex-shrink-0", isCollapsed ? "w-5 h-5" : "w-5 h-5 mr-3")} />
          {!isCollapsed && (
            <span className="font-medium">Sign Out</span>
          )}
        </Button>

        {/* User Avatar and Info */}
        <div className={cn("flex items-center", isCollapsed ? "justify-center" : "space-x-3")}>
          <Avatar className="w-10 h-10 flex-shrink-0">
            <AvatarImage src="https://res.cloudinary.com/dfilgqymt/image/upload/f_auto,q_auto/user-avatar-robot-icon-2048x2048-ehqvhi4d_oyocf3" alt={user?.displayName || "User"} />
            <AvatarFallback className="bg-gradient-to-r from-indigo-500 to-purple-500 text-white font-semibold">
              {user?.displayName ? getInitials(user.displayName) : "U"}
            </AvatarFallback>
          </Avatar>
          {!isCollapsed && (
            <div className="min-w-0 flex-1">
              <p className="text-sm font-medium text-gray-900 truncate">
                {user?.displayName || "User"}
              </p>
              <p className="text-xs text-gray-500 truncate">
                {userDocument?.tier || "Starter"} Plan
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )

  return (
    <>
      {/* Mobile Menu Button */}
      <Button
        variant="ghost"
        size="sm"
        className="fixed top-4 left-4 z-50 md:hidden bg-white shadow-lg"
        onClick={toggleMobile}
      >
        {isMobileOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
      </Button>

      {/* Mobile Overlay */}
      {isMobileOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={toggleMobile}
        />
      )}

      {/* Desktop Sidebar */}
      <aside
        className={cn(
          "hidden md:flex flex-col bg-white border-r border-gray-200 transition-all duration-300 ease-in-out h-screen fixed left-0 top-0",
          isCollapsed ? "w-20" : "w-72",
          className
        )}
      >
        {/* Collapse Toggle */}
        <Button
          variant="ghost"
          size="sm"
          className="absolute -right-3 top-6 z-10 w-6 h-6 rounded-full bg-white border border-gray-200 shadow-sm hover:shadow-md"
          onClick={toggleCollapse}
        >
          {isCollapsed ? (
            <ChevronRight className="w-3 h-3" />
          ) : (
            <ChevronLeft className="w-3 h-3" />
          )}
        </Button>

        <SidebarContent />
      </aside>

      {/* Mobile Sidebar */}
      <aside
        className={cn(
          "fixed top-0 left-0 z-50 w-72 h-full bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out md:hidden",
          isMobileOpen ? "translate-x-0" : "-translate-x-full"
        )}
      >
        <SidebarContent />
      </aside>
    </>
  )
}

// Add default export for Next.js page
export default function SideBarPage() {
  return (
    <div className="min-h-screen bg-gray-50 flex">
      <Sidebar />
      <div className="flex-1 ml-0 md:ml-72">
        {/* Main content area */}
        <div className="p-8">
          <h1 className="text-2xl font-bold">Sidebar Component Preview</h1>
          <p className="text-gray-600 mt-2">This is a preview of the sidebar component.</p>
        </div>
      </div>
    </div>
  )
}
